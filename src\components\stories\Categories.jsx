import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { usePostCategoriesMutation } from "../../store/apis/storiesApi";
import StarButton from "./StarButton";
import { setErrors, setSubCategory } from "../../store/slices/storiesSlice";
import { setVideoSubCategory } from "../../store/slices/videoStorySlice";
import { storyType, storyTypeNumbers } from "../../utils/constants";
import { setWebStoryError, setWebStorySubCategory } from "../../store/slices/webstorySlice";

const showStarButton = {
	stories: true,
	video: false,
	webStory: false,
};
const Categories = ({ type = "stories", isShorts = false }) => {
	const dispatch = useDispatch();
	const [postCategories, { isLoading, isError, error, data }] = usePostCategoriesMutation();

	const {
		storiesState: { isHighlighted, highligthedSubCategory, subcategory },
		errors,
	} = useSelector((state) => state[storyType[type]]);

	// fething the data from the api for the categories
	useEffect(() => {
		postCategories({
			categoryType: isShorts ? 2 : storyTypeNumbers[type] ?? null,
		});
	}, [type, postCategories]);

	// setting the subcategory
	const handleSubCategoryClick = (subCategoryId) => {
		if (type === "videoStory") {
			dispatch(setVideoSubCategory(subCategoryId));
		}
		if (type === "stories") {
			dispatch(setSubCategory(subCategoryId));
			dispatch(setErrors({ ...errors, subcategory: null }));
		}
		if (type === "webStory") {
			dispatch(setWebStorySubCategory(subCategoryId));
			dispatch(setWebStoryError({ ...errors, subcategory: null }));
		}
	};

	return (
		<div className="py-5">
			<h3>Assign story to one or more subcategories for readers to find them.</h3>
			<div className="text-sm flex flex-col gap-y-4 mt-4 ">
				{data
					? data.data.map((category) => {
							return (
								<div key={category._id}>
									<div className="text-[#00000080]">{category.name}</div>
									<div className="pl-2 mt-2 flex flex-col gap-y-2 w-full">
										{category.subcategory.map((subCategory) => {
											return (
												<div
													key={subCategory._id}
													className="flex items-center gap-x-3 group w-full justify-between pr-6"
												>
													<div className="flex items-center gap-x-2">
														<input
															type="radio"
															value={subCategory._id}
															name={"subcategory"}
															id={subCategory._id}
															checked={subcategory === subCategory._id ? true : false}
															className="w-4 h-4"
															onClick={() => handleSubCategoryClick(subCategory._id)}
														/>{" "}
														<label htmlFor={subCategory._id} className="text-fadeGray">
															{subCategory.name}
														</label>
													</div>
													{showStarButton[type] ? (
														<div
															className={`group-hover:block ${
																highligthedSubCategory === subCategory._id ? "block" : "hidden"
															}`}
														>
															<StarButton
																type={type}
																highligthedSubCategory={highligthedSubCategory}
																isHighlighted={isHighlighted || false}
																subCategoryId={subCategory._id}
															/>
														</div>
													) : null}
												</div>
											);
										})}
									</div>
									{type === "webStory" && errors.subcategory && (
										<p className="text-red-500 text-sm mt-2">{errors.subcategory}</p>
									)}
								</div>
							);
					  })
					: null}
			</div>
		</div>
	);
};

export default Categories;
