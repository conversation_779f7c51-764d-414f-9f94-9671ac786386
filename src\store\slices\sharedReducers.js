// used to set the subcategory in all the stories slices
export const setSubCategoryState = (state, payload) => {
	state.storiesState.subcategory = payload;
};

// used to set the highlighted subcategory in all the stories slices
export const setHighlightedSubCategoryState = (state, payload) => {
	// If the payload (new category ID) is the same as the currently highlighted one, unhighlight it.
	if (state.storiesState.highligthedSubCategory === payload) {
		state.storiesState.highligthedSubCategory = null;
		state.storiesState.isHighlighted = false;
	} else {
		// Otherwise, set the new category as the highlighted one and ensure isHighlighted is true.
		state.storiesState.highligthedSubCategory = payload;
		state.storiesState.isHighlighted = true;
	}
};

export const setMetaDataState = (state, payload) => {
	const { name, value } = payload;
	state.storiesState.meta[name] = value;
};

// set keywords here
export const setKeyWordsState = (state, payload) => {
	state.storiesState.meta.keywords = payload;
};

// set the canonical here
export const setCanonicalState = (state, payload) => {
	state.storiesState.meta.canonical = !state.storiesState.meta.canonical;
};

// set indexing
export const setIndexingState = (state, payload) => {
	const indexingValue = state.storiesState.meta.robots;
	if (indexingValue === "index,follow") {
		state.storiesState.meta.robots = "noindex,nofollow";
	} else if (indexingValue === "noindex,nofollow") {
		state.storiesState.meta.robots = "index,follow";
	}
};
//set twitter and og image
export const setImageState = (state, payload) => {
	const { name, value } = payload;
	state.storiesState[name] = value;
};

// set og content
export const setOgContentState = (state, payload) => {
	const { name, value } = payload;
	state.storiesState.og[name] = value;
};
// set x content
export const setXContentState = (state, payload) => {
	const { name, value } = payload;
	state.storiesState.twitter[name] = value;
};

//set toggle of the flags
export const toggleFlagState = (state, payload) => {
	const flagId = payload;
	if (state.storiesState.section.includes(flagId)) {
		state.storiesState.section = state.storiesState.section.filter((id) => id !== flagId);
	} else {
		state.storiesState.section.push(flagId);
	}
};

export const toggleSeconadarySubCategoryState = (state, payload) => {
	const secondarySubCategoryId = payload;
	if (state.storiesState.secondarySubCategories.includes(secondarySubCategoryId)) {
		state.storiesState.secondarySubCategories = state.storiesState.secondarySubCategories.filter(
			(id) => id !== secondarySubCategoryId
		);
	} else {
		state.storiesState.secondarySubCategories.push(secondarySubCategoryId);
	}
};

export const filterStoryStatusState = (state, payload) => {
	state.filter.search = "";
	if (payload === "all") {
		state.filter.status = null;
	}
	if (payload === "published") {
		state.filter.status = 1;
	}
	if (payload === "unpublished") {
		state.filter.status = 0;
	}
	if (payload === "drafts") {
		state.filter.status = 3;
	}
	if (payload === "scheduled") {
		state.filter.status = 4;
	}
	state.offset = 0;
	state.hasMore = true;
};

export const storiesInputFilterState = (state, payload) => {
	state.filter.search = payload;
	state.offset = 0;
	state.data = [];
	state.hasMore = true;
};

export const setFetchedDataState = (state, payload) => {
	const { data, replace = false } = payload;
	state.data = replace ? data : [...state.data, ...data];
	state.hasMore = data.length === 20; // Assuming 20 items per page
};

export const increaseFilterLimitCountState = (state) => {
	state.offset = state.offset + state.limit;
};

export const incrementOffsetState = (state) => {
	state.offset += state.limit;
};

export const resetFiltersState = (state) => {
	state.filter.search = "";
	state.filter.status = null;
	if (state.filter?.section) {
		state.filter.section = [];
	}
	state.limit = 20;
	state.hasMore = true;
	state.offset = 0;
};
export const setIntialFiltersStateData = (state, payload) => {
	state.filter.search = payload.search;
	state.filter.status = payload.status;
	state.limit = 20;
	state.hasMore = true;
	state.offset = 0;
};

export const setMetaDataTemplateData = (state, payload) => {
	state.storiesState.meta.title = payload.title;
	state.storiesState.meta.description = payload.description;
};
