import React, { useState } from "react";
import { BiCategory, BiChevronLeft, BiChevronRight, BiMenu, BiPlus } from "react-icons/bi";
import { CiSearch } from "react-icons/ci";
import { HiOutlineTemplate } from "react-icons/hi";
import { IoFlagOutline, IoSettingsOutline } from "react-icons/io5";

import Button from "../../parts/Button";
import { BsTags } from "react-icons/bs";
import { useDispatch, useSelector } from "react-redux";
import { RxCross2 } from "react-icons/rx";
import RenderTabs from "./RenderTabs";
import { setShowSideStoryTab, setStoryTab } from "../../store/slices/storiesSlice";
import { MdOutlineCategory, MdOutlineReviews } from "react-icons/md";

// menu items for the sidebar
const navMenus = [
	{ name: "Add", icons: <BiPlus /> },
	{
		name: "Categories",
		icons: <BiCategory />,
	},
	{
		name: "SubCategories",
		icons: <MdOutlineCategory />,
	},
	{ name: "Flags", icons: <IoFlagOutline /> },
	{ name: "Tags", icons: <BsTags /> },
	{ name: "Settings", icons: <IoSettingsOutline /> },
	{ name: "Reviews", icons: <MdOutlineReviews /> },
	{ name: "Templates", icons: <HiOutlineTemplate /> },
	{ name: "SEO", icons: <CiSearch /> },
];

const MenuItem = ({ menu, setShowSideBar, selectedStoryTab }) => {
	const dispatch = useDispatch();
	const handleTabClick = (name) => {
		dispatch(setStoryTab(name));
		dispatch(setShowSideStoryTab(true));
		// dispatch(showStoryTab(true));
	};
	return (
		<div className="flex flex-col gap-y-1 group ">
			<Button
				type="button"
				variant="outline"
				value={menu.name}
				name={menu.name}
				onClick={() => handleTabClick(menu.name)}
				rounded="full"
				customClasses={`${
					selectedStoryTab === menu.name ? "text-primary border-primary" : null
				} flex items-center mx-auto h-10 w-10 border border-gray-200 text-gray-500 hover:text-primary hover:border-primary transition-all duration-200 hover:!bg-transparent
       `}
			>
				<span className="text-xl">{menu.icons}</span>
			</Button>
			<span
				className={`${
					selectedStoryTab === menu.name ? "text-primary" : null
				} text-center text-xs group-hover:text-primary`}
			>
				{menu.name}
			</span>
		</div>
	);
};
const Sidebar = ({ editor }) => {
	const dispatch = useDispatch();
	const { selectedStoryTab, showSideStoryTab } = useSelector((state) => state.stories);

	return (
		<div className="relative">
			<div className="fixed flex top-16">
				<div
					className={`md:relative z-20 flex flex-col calculateHeight2 relative overflow-hidden border bg-white transition-all duration-300
            w-22 md:left-0`}
				>
					<div className="flex items-center justify-between p-4"></div>
					<div className="flex-1 overflow-y-auto relative scrollbar">
						<div className="flex flex-col items-center w-full gap-y-4 px-2 border-gray-700">
							{navMenus.map((menu, index) => (
								<MenuItem key={index} menu={menu} selectedStoryTab={selectedStoryTab} />
							))}
						</div>
					</div>
				</div>
			</div>

			{/* Slide-out Sidebar Panel */}
			{showSideStoryTab ? (
				<div
					className={`${
						showSideStoryTab ? "translate-x-20 w-96 opacity-100" : "translate-x-0 w-0 opacity-0"
					} transition-all ease-in duration-1000 border-r h-screen overflow-y-auto ml-5 scrollbar`}
				>
					{/* Fixed Header Section */}
					<div className="sticky top-16 z-10 bg-white border-b">
						<div className="flex items-center justify-between w-full px-3 py-3">
							<div className="text-lg">{selectedStoryTab}</div>
							<RxCross2
								onClick={() => dispatch(setShowSideStoryTab(false))}
								className="cursor-pointer text-2xl text-gray-700"
							/>
						</div>
					</div>

					{/* Scrollable Content Section */}
					<div className="mt-10 overflow-y-auto h-full scrollbar">
						<RenderTabs editor={editor} />
					</div>
				</div>
			) : null}
		</div>
	);
};
export default Sidebar;
