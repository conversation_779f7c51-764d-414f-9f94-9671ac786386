:root {
	--left-container-width: 72%;
	--right-container-width: 28%;
	--aside-container-width: 20%;
	--aside-bg: #eceff3;
	--border-color: rgba(0, 0, 0, 0.1);
}

.modal {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(0, 0, 0, 0.5);
	width: 100vw;
	height: 100vh;
	position: fixed;
	z-index: 9999;
	top: 0;
	left: 0;
	animation-name: fadeIn;
	animation-duration: 0.2s;
	animation-iteration-count: 1;
	animation-timing-function: all ease;
}
.card {
	width: 90vw;
	height: 90vh;
	background-color: white;
	border-radius: 10px;
	box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
	animation-name: zoomin;
	animation-duration: 0.5s;
	animation-iteration-count: 1;
	animation-timing-function: ease;
	overflow-y: hidden;
}
.errorMessage {
	display: block;
	margin-top: 5px;
	font-size: 13px;
	color: red;
}
.header {
	position: sticky;
	top: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 10px;
	padding: 14px;
	border-bottom: 1px solid var(--border-color);
}
.closeBtn {
	width: 20px;
	height: 20px;
	cursor: pointer;
}
.title {
	font-size: 1.2rem;
	font-weight: 600;
}
.cardBody {
	display: flex;
	width: 100%;
	height: calc(100% - 57.8px);
	position: relative;
	/* overflow-y: scroll; */
}
.asideContainer {
	position: relative;
	display: block;
	width: var(--aside-container-width);
	background-color: var(--aside-bg);
	border-right: 1px solid var(--border-color);
}
.asideFooter {
	width: 100%;
	position: sticky;
	bottom: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 14px;
}
.asideContainerWrapper {
	position: relative;
	display: block;
	width: 100%;
	height: calc(720px - (57.8px + 61px));
	padding-inline: 15px;
}
.mainContainer {
	width: 100%;
	height: 100%;
}
.leftContainer {
	position: relative;
	display: block;
	width: var(--left-container-width);
	height: 100%;
	padding-inline: 25px;
}
.rightContainer {
	position: relative;
	display: flex;
	align-items: flex-start;
	width: var(--right-container-width);
	border-left: 1px solid var(--border-color);
	padding: 25px;
}
.containerWrapper {
	display: flex;
	height: calc(100% - 61px);
}
.galleryContainer {
	position: relative;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: calc(100% - 98.4px);
	overflow-y: auto;
}
.tableBlock {
	display: block;
	height: 100%;
}
.imageSec {
	position: relative;
	width: 100px;
	min-width: 100px;
	height: 100px;
	overflow: hidden;
}
.imageSec img {
	width: 100%;
	height: 100%;
	object-fit: contain;
}
.imageSec svg {
	width: 100%;
	height: 100%;
}
.contentSec {
	display: block;
	text-align: left;
	width: 100%;
}
.filterWrapper {
	position: sticky;
	top: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 30px;
	width: 100%;
	background-color: #fff;
}
.searchBar {
	width: 100%;
	margin-block: 30px;
}
.filterText {
	font-size: 15px;
	font-weight: 100;
	font-family: sans-serif;
}
.filterBlock {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 10px;
	width: 35%;
}
.contentSec h4 {
	font-size: 22px;
	font-weight: 600;
	margin-bottom: 10px;
}
.contentSec p {
	font-size: 16px;
	font-weight: 300;
	color: rgba(0, 0, 0, 0.65);
	margin-bottom: 15px;
}
.box {
	margin-top: 10px;
	padding: 6px 12px;
	background-color: rgb(230, 244, 255);
	border: 1px solid #3475de;
	border-radius: 8px;
}
.box span {
	display: block;
	font-size: 15px;
	font-weight: 600;
}
.box p {
	font-size: 15px;
}
.footer {
	width: 100%;
	position: sticky;
	bottom: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	border-top: 1px solid var(--border-color);
	padding: 14px;
	background-color: white;
}
.footerSelectedText {
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-weight: 100;
	font-family: sans-serif;
}
.buttonGroup {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 10px;
}
.btnPrimaryOutline {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 80px;
	height: 32px;
	padding: 4px 16px;
	font-size: 14px;
	background-color: transparent;
	border-radius: 25px;
	border: 1px solid #3475de;
	color: #3475de;
	transition: all 0.3s ease;
}
.btnPrimary {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 80px;
	height: 32px;
	padding: 4px 16px;
	font-size: 14px;
	background-color: #3475de;
	border-radius: 25px;
	border: 1px solid #3475de;
	color: #fff;
	transition: all 0.3s ease;
}

.btnPrimaryOutline:hover {
	background-color: #3475de;
	border-radius: 25px;
	border: 1px solid #3475de;
	color: #fff;
}
.btnPrimary:hover {
	border: 1px solid #3475de;
	background-color: #3475de;
}
.btnPrimary:disabled {
	border: unset;
	background-color: rgba(0, 6, 36, 0.3);
	cursor: auto;
}

.grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	/* grid-gap: 10px; */
}

.row {
	display: grid;
	grid-template-columns: repeat(1, 1fr);
	/* grid-gap: 10px; */
}

.grid_VR {
	width: 1px;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.25);
}

.cardItem {
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	gap: 10px;
	--border: 1px dashed rgba(0, 0, 0, 0.25);
	padding-top: 10px;
	padding-bottom: 10px;
	margin-right: 10px;
}
.grid .cardItem:nth-child(n + 3) {
	border-top: var(--border);
}
.row .cardItem {
	border-bottom: var(--border);
}
.row .cardItem:last-child {
	border-bottom: none;
}
.cardImg_Wrapper {
	position: relative;
	width: 120px;
	height: 80px;
	min-width: 120px;
	overflow: hidden;
}
.cardImg_Wrapper img {
	position: absolute;
	inset: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.cardContent_Wrapper {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: flex-start;
}
.cardContent_Wrapper span {
	display: block;
	font-size: 12px;
	color: red;
}
.cardContent_Wrapper h3 {
	font-size: 16px;
	margin-top: 0px;
	margin-bottom: 0px;
}

@keyframes zoomin {
	0% {
		transform: scale(0.5);
	}
	100% {
		transform: scale(1);
	}
}
@keyframes fadeIn {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
