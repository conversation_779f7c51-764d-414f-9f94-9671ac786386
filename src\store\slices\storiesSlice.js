import { createSlice } from "@reduxjs/toolkit";
import {
	filterStoryStatusState,
	increaseFilterLimitCountState,
	incrementOffsetState,
	resetFiltersState,
	setCanonicalState,
	setFetchedDataState,
	setHighlightedSubCategoryState,
	setImageState,
	setIndexingState,
	setIntialFiltersStateData,
	setKeyWordsState,
	setMetaDataState,
	setOgContentState,
	setSubCategoryState,
	setXContentState,
	storiesInputFilterState,
	toggleFlagState,
	toggleSeconadarySubCategoryState,
} from "./sharedReducers";
import { generateSlugStories } from "../../utils/helperFunctions";

const initialState = {
	filter: {
		status: null,
		search: "",
		_id: null,
	},
	data: [],
	errors: {},
	limit: 20,
	offset: 0,
	status: null,
	hasMore: true,
	selectedStoryTab: null,
	showPhotoGallery: false,
	photoGalleryEditingSlide: null,
	showMore: false,
	showStoryTab: false,
	showSideStoryTab: false,
	showAstro: false,
	showEmbed: false,
	isMetaTitleActive: false,
	showRelatedPosts: false,
	showGallery: false,
	fetchedTagData: [],
	editCoverImg: false,
	croppedImg: [],
	storiesState: {
		tags: [],
		title: null,
		categories: {},
		status: null,
		content: {},
		isHighlighted: false,
		highligthedSubCategory: null,
		subcategory: "",
		secondarySubCategories: [],
		section: [],
		caption: null,
		courtesy: null,
		altName: null,
		publishDate: null,
		coverImg: [],
		writer: [],
		contributors: [],
		excerpt: null,
		articleTitle: null,
		articleSubheading: null,
		platform: null,
		views: null,
		duplicationNote: null,
		week: null,
		weekendBoxOffice: null,
		cumulativeBoxOffice: null,
		template: 1,
		viewLink: null,
		photoGallery: [],
		meta: {
			title: null,
			description: null,
			keywords: [],
			primaryKeywords: [],
			slug: null,
			author: null,
			canonical: null,
			robots: "index,follow",
		},
		isPromotional: false,
		ogImg: [],
		coverImgFocalPosition: {
			scale: 1,
			top: 0,
			left: 0,
		},
		twitterImg: [],
		og: {
			title: null,
			description: null,
		},
		twitter: {
			title: null,
			description: null,
			card: "large",
		},
		reviews: {
			title: null,
			releasePlatform: null,
			reviewerName: null,
			rating: null,
			summary: null,
			publishDate: null,
			cast: null,
			director: null,
			screenWriter: null,
			language: null,
			runtime: null,
		},
	},
};

export const storiesSlice = createSlice({
	name: "stories",
	initialState,
	reducers: {
		setShowPhotoGallery: (state, { payload }) => {
			state.showPhotoGallery = payload;
		},
		setPhotoGalleryEditingSlide: (state, { payload }) => {
			state.photoGalleryEditingSlide = payload;
		},
		setSecondarySubCategories: (state, { payload }) => {
			state.storiesState.secondarySubCategories = payload;
		},

		setMetaTitleActive: (state, { payload }) => {
			state.isMetaTitleActive = true;
		},
		setCoverImgTransformData: (state, { payload }) => {
			state.storiesState.coverImgFocalPosition = payload;
		},
		setEditCoverImg: (state, { payload }) => {
			state.editCoverImg = payload;
		},
		filterStoryStatus: (state, { payload }) => filterStoryStatusState(state, payload),
		storiesInputFilter: (state, { payload }) => storiesInputFilterState(state, payload),
		setFilterPostIds: (state, { payload }) => {
			state.filter._id = payload;
		},
		setFetchedData: (state, { payload }) => setFetchedDataState(state, payload),

		increaseFilterLimitCount: (state, { payload }) => increaseFilterLimitCountState(state),

		incrementOffset: (state) => incrementOffsetState(state),
		resetFilters: (state, { payload }) => resetFiltersState(state),
		storyStatus: (state, { payload }) => {
			state.status = payload;
		},

		showStoryTab: (state, { payload }) => {
			state.showStoryTab = payload;
		},
		setStoryTab: (state, { payload }) => {
			state.selectedStoryTab = payload;
		},

		setArticleTitle: (state, { payload }) => {
			state.storiesState.articleTitle = payload;
			state.storiesState.title = payload;
		},

		// used for the setting the highlighted subcategory
		setHighlightedSubCategory: (state, { payload }) =>
			setHighlightedSubCategoryState(state, payload),
		// used for setting the subcategory
		setSubCategory: (state, { payload }) => setSubCategoryState(state, payload),

		// used for toggling the flags
		toggleFlag: (state, { payload }) => toggleFlagState(state, payload),
		toggleSecondarySubCategory: (state, { payload }) =>
			toggleSeconadarySubCategoryState(state, payload),

		// used for setting the initially selected ids on the data fetching
		setSelectedFlags: (state, { payload }) => {
			state.storiesState.section = payload;
		},

		setIsPromotional: (state, { payload }) => {
			state.storiesState.isPromotional = payload;
		},

		// to set the cover image
		setCoverImg: (state, { payload }) => {
			state.storiesState.coverImg = payload;
		},

		// set the value of the writer
		setWriter: (state, { payload }) => {
			state.storiesState.writer = payload;
		},

		// set the value of the contributors here
		setContributors(state, { payload }) {
			state.storiesState.contributors = payload;
		},

		// set setttings data here
		setSettingsData: (state, { payload }) => {
			const { name, value } = payload;
			state.storiesState[name] = value;
		},

		// set template data here
		setTemplate: (state, { payload }) => {
			state.storiesState.template = payload;
		},

		// set meta data here
		setMetaData: (state, { payload }) => setMetaDataState(state, payload),

		// set keywords here
		setKeyWords: (state, { payload }) => setKeyWordsState(state, payload),

		// set the canonical here
		setCanonical: (state, { payload }) => setCanonicalState(state, payload),

		// set indexing
		setIndexing: (state, { payload }) => setIndexingState(state, payload),
		//set twitter and og image
		setImage: (state, { payload }) => setImageState(state, payload),

		// set og content
		setOgContent: (state, { payload }) => setOgContentState(state, payload),
		// set x content
		setXContent: (state, { payload }) => setXContentState(state, payload),

		setTags: (state, { payload }) => {
			state.fetchedTagData = payload.tags;
			state.offset = payload.offset || state.offset;
			state.showMore = payload.showMore;
		},
		resetTags: (state) => {
			state.fetchedTagData = [];
			state.offset = 0;
			state.showMore = true;
		},
		setEditorContent: (state, { payload }) => {
			state.storiesState.content = payload;
		},
		setShowAstro: (state, { payload }) => {
			state.showAstro = payload;
		},
		setShowEmbed: (state, { payload }) => {
			state.showEmbed = payload;
		},
		setShowGallery: (state, { payload }) => {
			state.showGallery = payload;
		},
		setShowRelatedPosts: (state, { payload }) => {
			state.showRelatedPosts = payload;
		},

		setTagData: (state, { payload }) => {
			if (!state.storiesState.tags.includes(payload)) {
				state.storiesState.tags.push(payload);
			}
		},
		removeTagData: (state, { payload }) => {
			state.storiesState.tags = state.storiesState.tags.filter((tag) => tag !== payload);
		},
		setInitialStoryData: (state, { payload }) => {
			state.status = payload?.status || null;
			state.storiesState.tags = payload?.tag || []; // Map tags
			state.storiesState.subcategory = payload?.subcategory || ""; // Map subcategory
			state.storiesState.secondarySubCategories = payload?.secondarySubCategories || []; // Map subcategory
			state.storiesState.section = payload?.section || []; // Map section
			state.storiesState.writer = payload?.writer || []; // Map section
			state.storiesState.contributors = payload?.contributor || []; // Map section
			state.storiesState.caption = payload?.caption || null; // Map caption
			state.storiesState.courtesy = payload?.courtesy || null; // Map courtesy
			state.storiesState.altName = payload?.altName || null; // Map alt name
			state.storiesState.publishDate = payload?.publishDate || null; // Map publish date
			state.storiesState.coverImg = payload?.coverImg ? payload.coverImg : []; // Map cover image as an array
			state.croppedImg = payload?.croppedImg ? payload.croppedImg : []; // Map cover image as an array
			state.storiesState.content = payload?.content ? JSON.parse(payload.content) : {}; // Parse content JSON
			state.storiesState.title = payload?.title || null; // Map title
			state.storiesState.duplicationNote = payload?.duplicationNote || null; // Map title
			state.storiesState.excerpt = payload?.excerpt || null; // Map excerpt
			state.storiesState.articleTitle = payload?.meta?.title || payload?.title || null; // Use meta title or fallback to title
			state.storiesState.articleSubheading = payload?.meta?.description || null; // Map description as subheading
			state.storiesState.platform = payload?.platform || null; // Map platform
			state.storiesState.views = payload?.views || null; // Map views
			state.storiesState.week = payload?.week || null; // Map week
			state.storiesState.weekendBoxOffice = payload?.weekendBoxOffice || null; // Map weekend box office
			state.storiesState.cumulativeBoxOffice = payload?.cumulativeBoxOffice || null; // Map cumulative box office
			state.storiesState.template = payload?.template || 1; // Map template with default value 1
			state.storiesState.viewLink = payload?.viewLink || null; // Map viewLink with default value null
			state.storiesState.isPromotional = payload?.isPromotional || false;
			// Map meta fields
			state.storiesState.meta = {
				title: payload?.meta?.title || null,
				slug: generateSlugStories(payload.slug) || null,
				description: payload?.meta?.description || null,
				keywords: payload?.meta?.keywords || [],
				primaryKeywords: payload?.meta?.primaryKeywords || [],
				author: payload?.meta?.author || null,
				canonical: payload?.meta?.canonical || null,
				robots: payload?.meta?.robots || "index,follow", // Default value
			};

			// Map Open Graph (OG) and Twitter Images
			state.storiesState.ogImg = payload?.meta?.og?.image ? payload.meta.og.image : [];
			state.storiesState.twitterImg = payload?.meta?.twitter?.image
				? payload.meta.twitter.image
				: [];

			state.storiesState.coverImgFocalPosition = payload?.coverImgFocalPosition || {
				top: 0,
				left: 0,
				scale: 1,
			};

			// Map Open Graph fields
			state.storiesState.og = {
				title: payload?.meta?.og?.title || null,
				description: payload?.meta?.og?.description || null,
			};
			state.storiesState.status = payload?.status || null; // Map status

			// Map Twitter fields
			state.storiesState.twitter = {
				title: payload?.meta?.twitter?.title || null,
				description: payload?.meta?.twitter?.description || null,
				card: payload?.meta?.twitter?.card || "large", // Default value
			};

			// Map reviews field
			state.storiesState.reviews = {
				title: payload?.reviews?.title || null,
				releasePlatform: payload?.reviews?.releasePlatform || null,
				reviewerName: payload?.reviews?.reviewerName || null,
				rating: payload?.reviews?.rating || null,
				summary: payload?.reviews?.summary || null,
				publishDate: payload?.reviews?.publishDate || null,
				cast: payload?.reviews?.cast || null,
				director: payload?.reviews?.director || null,
				screenWriter: payload?.reviews?.screenWriter || null,
				language: payload?.reviews?.language || null,
				runtime: payload?.reviews?.runtime || null,
			};

			// Map photo gallery
			state.storiesState.photoGallery = payload?.photoGallery || [];
		},

		setErrors: (state, action) => {
			state.errors = action.payload; // Set validation errors
		},
		clearErrors: (state) => {
			state.errors = {};
		},
		resetState: () => initialState,
		resetRestState: (state) => {
			return {
				filter: state.filter,
				limit: state.limit,
				offset: state.offset,
				status: state.status,
				...initialState,
			};
		},
		setInititalFilterData: (state, { payload }) => setIntialFiltersStateData(state, payload),

		setShowSideStoryTab: (state, { payload }) => {
			state.showSideStoryTab = payload;
		},
		setPublishedData: (state, { payload }) => {
			state.storiesState.publishDate = payload;
		},
		setCroppedImage: (state, { payload }) => {
			state.croppedImg = payload;
		},
		setViewLink: (state, { payload }) => {
			state.storiesState.viewLink = payload;
		},
		setReviewsData: (state, { payload }) => {
			const { name, value } = payload;
			state.storiesState.reviews[name] = value;
		},
		setPhotoGallery: (state, { payload }) => {
			state.storiesState.photoGallery = payload;
		},
		addPhotoGalleryItem: (state, { payload }) => {
			state.storiesState.photoGallery.push(payload);
		},
		updatePhotoGalleryItem: (state, { payload }) => {
			const { index, data } = payload;
			state.storiesState.photoGallery[index] = data;
		},
		removePhotoGalleryItem: (state, { payload }) => {
			state.storiesState.photoGallery = state.storiesState.photoGallery.filter(
				(_, index) => index !== payload
			);
		},
		reorderPhotoGallery: (state, { payload }) => {
			state.storiesState.photoGallery = payload;
		},
	},
});
// Action creators are generated for each case reducer function
export const {
	setMetaTitleActive,
	setCoverImgTransformData,
	setEditCoverImg,
	increaseFilterLimitCount,
	filterStoryStatus,
	storiesInputFilter,
	setFilterPostIds,
	setArticleTitle,
	setFetchedData,
	storyStatus,
	showStoryTab,
	setStoryTab,
	setHighlightedSubCategory,
	setSubCategory,
	toggleFlag,
	toggleSecondarySubCategory,
	setSelectedFlags,
	setCoverImg,
	setWriter,
	setContributors,
	setSettingsData,
	setTemplate,
	setMetaData,
	setKeyWords,
	setCanonical,
	setIndexing,
	setImage,
	setOgContent,
	setXContent,
	setTags,
	resetTags,
	incrementOffset,
	resetFilters,
	setEditorContent,
	setShowAstro,
	setShowEmbed,
	setShowRelatedPosts,
	setShowGallery,
	setTagData,
	removeTagData,
	setInitialStoryData,
	clearErrors,
	resetState,
	setErrors,
	setInititalFilterData,
	setShowSideStoryTab,
	setPublishedData,
	setCroppedImage,
	setViewLink,
	resetRestState,
	setIsPromotional,
	setReviewsData,
	setShowPhotoGallery,
	setPhotoGalleryEditingSlide,
	setPhotoGallery,
	addPhotoGalleryItem,
	updatePhotoGalleryItem,
	removePhotoGalleryItem,
	reorderPhotoGallery,
	setSecondarySubCategories,
} = storiesSlice.actions;

export default storiesSlice.reducer;
