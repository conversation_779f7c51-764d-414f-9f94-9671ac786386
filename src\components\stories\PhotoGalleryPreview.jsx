import React, { useState } from "react";
import { FiEdit2, FiTrash2, FiChevron<PERSON>eft, FiChevronRight } from "react-icons/fi";
import PhotoGalleryReorder from "./PhotoGalleryReorder";

const PhotoGalleryPreview = ({ images, onEdit, onDelete, onReorder }) => {
	const [currentIndex, setCurrentIndex] = useState(0);

	const handlePrevious = () => {
		setCurrentIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
	};

	const handleNext = () => {
		setCurrentIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
	};

	if (!images.length) {
		return (
			<div className="h-full flex items-center justify-center text-gray-500">
				<div className="text-center">
					<p className="text-lg mb-2">No images added yet</p>
					<p className="text-sm">Add images to see preview here</p>
				</div>
			</div>
		);
	}

	// Adjust currentIndex if it's out of bounds
	const safeIndex = currentIndex >= images.length ? images.length - 1 : currentIndex;
	const currentImage = images[safeIndex];

	// Update currentIndex if it was adjusted
	if (safeIndex !== currentIndex) {
		setCurrentIndex(safeIndex);
	}

	return (
		<div className="h-full flex flex-col">
			<div className=" flex flex-col justify-center">
				<div className="relative flex items-center justify-center">
					<button
						onClick={handlePrevious}
						disabled={images.length <= 1}
						className="absolute left-14 z-10 p-2 rounded-full disabled:opacity-30"
					>
						<FiChevronLeft className="w-6 h-6 text-black" />
					</button>

					<div className="relative group bg-gray-100 rounded-lg shadow-sm border overflow-hidden mx-12">
						<div className="relative  w-64 h-96 flex items-center justify-center">
							<img
								src={
									typeof currentImage.imagePath === "string"
										? currentImage.imagePath
										: URL.createObjectURL(currentImage.imagePath)
								}
								alt={currentImage.altName}
								className="max-w-full max-h-full object-contain"
							/>

							{/* Action buttons */}
							<div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
								<button
									onClick={() => onEdit(currentIndex)}
									className="p-2 bg-gray-800 bg-opacity-75 text-white rounded-full hover:bg-opacity-90"
								>
									<FiEdit2 size={14} />
								</button>
								<button
									onClick={() => onDelete(currentIndex)}
									className="p-2 bg-red-600 bg-opacity-75 text-white rounded-full hover:bg-opacity-90"
								>
									<FiTrash2 size={14} />
								</button>
							</div>

							{/* Title and Caption overlay */}
							<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-3 text-white">
								<h4 className="font-semibold text-sm mb-1">{currentImage.title}</h4>
								<div className="text-xs" dangerouslySetInnerHTML={{ __html: currentImage.caption }} />
							</div>
						</div>
					</div>

					<button
						onClick={handleNext}
						disabled={images.length <= 1}
						className="absolute right-14 z-10 p-2 rounded-full disabled:opacity-30"
					>
						<FiChevronRight className="w-6 h-6 text-black" />
					</button>
				</div>

				{/* Dots navigation */}
				<div className="flex justify-center mt-4">
					{images.map((_, idx) => (
						<button
							key={idx}
							className={`w-2 h-2 rounded-full mx-1 ${
								idx === currentIndex ? "bg-primary" : "bg-gray-300"
							}`}
							onClick={() => setCurrentIndex(idx)}
						/>
					))}
				</div>
			</div>

			{/* Reorder Section */}
			{images.length > 1 && (
				<div className="mt-4">
					<PhotoGalleryReorder images={images} onReorder={onReorder} />
				</div>
			)}
		</div>
	);
};

export default PhotoGalleryPreview;
