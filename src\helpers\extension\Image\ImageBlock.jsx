import { ReactNodeViewRenderer } from "@tiptap/react";
import { mergeAttributes } from "@tiptap/core";
import ImageBlockView from "./components/ImageBlockView";
import { Image } from "@tiptap/extension-image";

export const ImageBlock = Image.extend({
	name: "imageBlock",

	group: "block",

	defining: true,

	isolating: true,

	addAttributes() {
		return {
			src: {
				default: "",
				parseHTML: (element) => element.getAttribute("src"),
				renderHTML: (attributes) => ({
					src: attributes.src,
				}),
			},
			width: {
				default: "100%",
				parseHTML: (element) => element.getAttribute("data-width"),
				renderHTML: (attributes) => ({
					"data-width": attributes.width,
				}),
			},
			align: {
				default: "center",
				parseHTML: (element) => element.getAttribute("data-align"),
				renderHTML: (attributes) => ({
					"data-align": attributes.align,
				}),
			},
			caption: {
				default: "",
				parseHTML: (element) => element.getAttribute("data-caption"),
				renderHTML: (attributes) => ({
					"data-caption": attributes.caption,
				}),
			},
			courtesy: {
				default: "",
				parseHTML: (element) => element.getAttribute("data-courtesy"),
				renderHTML: (attributes) => ({
					"data-courtesy": attributes.courtesy,
				}),
			},
			alt: {
				default: "",
				parseHTML: (element) => element.getAttribute("data-alt"),
				renderHTML: (attributes) => ({
					"data-alt": attributes.alt,
				}),
			},
			widthPx: {
				default: null,
				parseHTML: (element) => element.getAttribute("data-width-px"),
				renderHTML: (attributes) => ({
					"data-width-px": attributes.widthPx,
				}),
			},
			heightPx: {
				default: null,
				parseHTML: (element) => element.getAttribute("data-height-px"),
				renderHTML: (attributes) => ({
					"data-height-px": attributes.heightPx,
				}),
			},
			fullWidth: {
				default: false,
				parseHTML: (element) => element.getAttribute("data-full-width") === "true",
				renderHTML: (attributes) => ({
					"data-full-width": attributes.fullWidth,
				}),
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'img[src*="tiptap.dev"]:not([src^="data:"]), img[src*="windows.net"]:not([src^="data:"])',
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		return ["img", mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)];
	},

	addCommands() {
		return {
			setImageBlock:
				(attrs) =>
				({ commands }) => {
					return commands.insertContent({
						type: "imageBlock",
						attrs: attrs,
					});
				},

			setImageBlockAt:
				(attrs) =>
				({ commands }) => {
					return commands.insertContentAt(attrs.pos, {
						type: "imageBlock",
						attrs: { src: attrs.src },
					});
				},

			setUpdateImage:
				(src) =>
				({ commands }) =>
					commands.updateAttributes("imageBlock", { src }),

			setImageBlockAlign:
				(align) =>
				({ commands }) =>
					commands.updateAttributes("imageBlock", { align }),

			setImageBlockWidth:
				(width) =>
				({ commands }) =>
					commands.updateAttributes("imageBlock", {
						width: width === 0 ? "100%" : `${Math.max(0, Math.min(100, width))}%`,
						fullWidth: width === 0,
					}),
			setImageBlockCaption:
				(caption) =>
				({ commands }) =>
					commands.updateAttributes("imageBlock", { caption }),

			setImageBlockCourtesy:
				(courtesy) =>
				({ commands }) =>
					commands.updateAttributes("imageBlock", { courtesy }),

			setImageBlockAlt:
				(alt) =>
				({ commands }) =>
					commands.updateAttributes("imageBlock", { alt }),

			setImageBlockPixelDimensions:
				({ widthPx, heightPx }) =>
				({ commands }) =>
					commands.updateAttributes("imageBlock", { widthPx, heightPx }),
		};
	},

	addNodeView() {
		return ReactNodeViewRenderer(ImageBlockView);
	},
});

export default ImageBlock;
