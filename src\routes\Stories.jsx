import React, { useCallback, useEffect, useMemo, useState } from "react";
import Sidebar from "../components/stories/Sidebar";
import TextEditorToolbar from "../components/stories/TextEditorToolbar";
import BlockEditor from "../components/editor/BlockEditor";
import "../styles/stories.css";
import TextEditor from "../components/stories/TextEditor";
import CustomModal from "../parts/CustomModal";
import ImageCropper from "../components/stories/ImageCropper";
import { useDispatch, useSelector } from "react-redux";
import {
  setCoverImgTransformData,
  setEditCoverImg,
  setEditorContent,
  setMetaData,
} from "../store/slices/storiesSlice";
import CoverImageCropper from "../components/stories/CoverImageCropper";
import PhotoGalleryPopup from "../components/stories/PhotoGalleryPopup";
import { useEditor } from "@tiptap/react";
import { extensions } from "../helpers/TipTapConfig";

const Stories = ({ method }) => {
  const {
    storiesState: { content },
    showPhotoGallery,
  } = useSelector((state) => state.stories);
  const dispatch = useDispatch();
  const editor = useEditor({
    onUpdate: useCallback(
      ({ editor }) => {
        const getContent = editor.getJSON();
        const description = getContent?.content?.find(
          (element) => element.type === "paragraph"
        );

        if (method === "POST") {
          if (description && description?.content?.length > 0) {
            const getDesc = description?.content
              .map((data) => data.text || "")
              .join("");
            dispatch(setMetaData({ name: "description", value: getDesc }));
          } else {
            dispatch(setMetaData({ name: "description", value: "" }));
          }
        }
        if (JSON.stringify(getContent) !== JSON.stringify(content)) {
          dispatch(setEditorContent(getContent));
        }
      },
      [content]
    ),
    extensions: extensions,
  });

  useEffect(() => {
    if (editor && content) {
      const currentContent = editor.getJSON();
      if (JSON.stringify(currentContent) !== JSON.stringify(content)) {
        editor.commands.setContent(content, false);
      }
    }
  }, [editor, content]);

  const value = useMemo(() => ({ editor }), [content]);
  return (
    <div className="text-sm">
      <div className="flex flex-col lg:flex-row relative h-screen bg-white">
        <Sidebar editor={value.editor} />
        <div className="w-full overflow-y-auto pl-20 py-3">
          <TextEditor method={method} editor={value.editor} />
        </div>
      </div>
      <CoverImageCropper type={"stories"} />
      {showPhotoGallery && <PhotoGalleryPopup />}
    </div>
  );
};

export default Stories;
