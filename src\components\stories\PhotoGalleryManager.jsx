import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { BiPlus } from "react-icons/bi";
import ImageDrop from "./ImageDrop";
import { Input } from "../../parts/FormComponents";
import PhotoGalleryPreview from "./PhotoGalleryPreview";
import PhotoGalleryModal from "./PhotoGalleryModal";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import {
	addPhotoGalleryItem,
	updatePhotoGalleryItem,
	removePhotoGalleryItem,
	reorderPhotoGallery,
	setPhotoGalleryEditingSlide,
} from "../../store/slices/storiesSlice";
import Button from "../../parts/Button";

const PhotoGalleryManager = () => {
	const dispatch = useDispatch();
	const { photoGallery, photoGalleryEditingSlide } = useSelector(
		(state) => state.stories.storiesState
	);
	const { photoGalleryEditingSlide: editingSlide } = useSelector((state) => state.stories);

	const [currentImage, setCurrentImage] = useState([]);
	const [currentData, setCurrentData] = useState({
		title: "",
		caption: "",
		altName: "",
		courtesy: "",
	});
	const [isEditing, setIsEditing] = useState(false);

	// Effect to populate form when editing
	useEffect(() => {
		if (editingSlide) {
			setIsEditing(true);
			setCurrentImage(editingSlide.data.image || [editingSlide.data.imagePath]);
			setCurrentData({
				title: editingSlide.data.title || "",
				caption: editingSlide.data.description || editingSlide.data.caption || "",
				altName: editingSlide.data.altName || "",
				courtesy: editingSlide.data.photoCredit?.join(", ") || editingSlide.data.courtesy || "",
			});
		} else {
			setIsEditing(false);
		}
	}, [editingSlide]);
	const [errors, setErrors] = useState({});
	const [editModalOpen, setEditModalOpen] = useState(false);
	const [editingIndex, setEditingIndex] = useState(null);

	const validateForm = () => {
		const newErrors = {};
		if (!currentImage.length) newErrors.image = "Image is required";
		if (!currentData.title.trim()) newErrors.title = "Title is required";
		if (!currentData.caption.trim()) newErrors.caption = "Caption is required";
		if (!currentData.altName.trim()) newErrors.altName = "Alt name is required";
		return newErrors;
	};

	const handleAddImage = () => {
		const validationErrors = validateForm();
		if (Object.keys(validationErrors).length > 0) {
			setErrors(validationErrors);
			return;
		}

		if (isEditing && editingSlide) {
			// Update existing slide
			const updatedItem = {
				...editingSlide.data,
				imagePath: currentImage[0] || editingSlide.data.imagePath,
				title: currentData.title,
				caption: currentData.caption,
				altName: currentData.altName,
				courtesy: currentData.courtesy,
			};
			dispatch(updatePhotoGalleryItem({ index: editingSlide.index, data: updatedItem }));
			setIsEditing(false);
			dispatch(setPhotoGalleryEditingSlide(null));
		} else {
			// Add new slide
			const newItem = {
				imagePath: currentImage[0],
				title: currentData.title,
				caption: currentData.caption,
				altName: currentData.altName,
				courtesy: currentData.courtesy,
				sortOrder: photoGallery.length,
			};
			dispatch(addPhotoGalleryItem(newItem));
		}

		// Clear form
		setCurrentImage([]);
		setCurrentData({ title: "", caption: "", altName: "", courtesy: "" });
		setErrors({});
	};

	const handleEdit = (index) => {
		const slideData = photoGallery[index];
		dispatch(setPhotoGalleryEditingSlide({ index, data: slideData }));
	};

	const handleDelete = (index) => {
		dispatch(removePhotoGalleryItem(index));
	};

	const handleReorder = (newOrder) => {
		const reorderedItems = newOrder.map((item, index) => ({
			...item,
			sortOrder: index,
		}));
		dispatch(reorderPhotoGallery(reorderedItems));
	};

	const handleDataChange = (field, value) => {
		setCurrentData((prev) => ({ ...prev, [field]: value }));
		if (errors[field]) {
			setErrors((prev) => ({ ...prev, [field]: "" }));
		}
	};

	return (
		<div className="flex w-full gap-6 h-full">
			{/* Left Side - 60% */}
			<div className="w-[60%] flex gap-7">
				{/* Image Dropper */}
				<div className="w-50%">
					<div className="w-64 h-96 aspect-auto photoGalleryImageDrop">
						<ImageDrop
							selectedFiles={currentImage}
							setSelectedFiles={setCurrentImage}
							label={false}
							customHeight="24rem"
							customHeight1="24rem"
							customClasses="relative !h-[24rem]"
						/>
					</div>
					{errors.image && <p className="text-red-500 text-sm mt-1">{errors.image}</p>}
				</div>
				{/* Image Information */}
				<div className="w-[50%] space-y-3">
					<Input
						customClass="w-full"
						label="Title"
						value={currentData.title}
						onDebouncedChange={(value) => handleDataChange("title", value)}
						placeholder="Enter image title"
						required
					/>
					{errors.title && <p className="text-red-500 text-sm">{errors.title}</p>}

					<div>
						<label className="text-sm text-gray-600 mb-2 block">Caption *</label>
						<ReactQuill
							theme="snow"
							value={currentData.caption}
							onChange={(value) => handleDataChange("caption", value)}
							placeholder="Enter image caption"
							className="!rounded-md border border-[#d1d5db] outline-none max-h-48 overflow-y-auto"
						/>
						{errors.caption && <p className="text-red-500 text-sm mt-1">{errors.caption}</p>}
					</div>

					<Input
						label="Alt Name"
						value={currentData.altName}
						onDebouncedChange={(value) => handleDataChange("altName", value)}
						placeholder="Enter alt text"
						required
					/>
					{errors.altName && <p className="text-red-500 text-sm">{errors.altName}</p>}

					<Input
						label="Courtesy"
						value={currentData.courtesy}
						onDebouncedChange={(value) => handleDataChange("courtesy", value)}
						placeholder="Enter image courtesy"
					/>

					<div className="flex w-full gap-5">
						<Button variant="primary" size="sm" rounded="full" onClick={handleAddImage}>
							{isEditing ? "Update Item" : "Add Item"}
						</Button>
						{isEditing && (
							<Button
								variant="secondary"
								size="sm"
								rounded="full"
								onClick={() => {
									setIsEditing(false);
									dispatch(setPhotoGalleryEditingSlide(null));
									setCurrentImage([]);
									setCurrentData({ title: "", caption: "", altName: "", courtesy: "" });
									setErrors({});
								}}
							>
								Cancel Edit
							</Button>
						)}
					</div>
				</div>
			</div>

			{/* Right Side - 40% */}
			<div className="w-[40%]">
				<PhotoGalleryPreview
					images={photoGallery}
					onEdit={handleEdit}
					onDelete={handleDelete}
					onReorder={handleReorder}
				/>
			</div>

			{/* Edit Modal */}
			{editModalOpen && (
				<PhotoGalleryModal
					isOpen={editModalOpen}
					onClose={() => {
						setEditModalOpen(false);
						setEditingIndex(null);
					}}
					imageData={photoGallery[editingIndex]}
					imageIndex={editingIndex}
				/>
			)}
		</div>
	);
};

export default PhotoGalleryManager;
