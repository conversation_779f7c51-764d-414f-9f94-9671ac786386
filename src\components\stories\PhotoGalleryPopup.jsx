import React from "react";
import { useDispatch } from "react-redux";
import styles from "../media-library/MediaLibrary.module.css";
import { FiX } from "react-icons/fi";
import { setShowPhotoGallery } from "../../store/slices/storiesSlice";
import PhotoGalleryManager from "./PhotoGalleryManager";
import Button from "../../parts/Button";

const PhotoGalleryPopup = () => {
	const dispatch = useDispatch();

	const handleClose = () => {
		dispatch(setShowPhotoGallery(false));
	};

	return (
		<div className={styles.modal} onClick={handleClose}>
			<div className={`${styles.card}`} onClick={(e) => e.stopPropagation()}>
				<div className={styles.header}>
					<div>
						<h4 className={styles.title}>Photo Gallery</h4>
						<small>
							<span className="font-bold">Note:</span> Images added here will appear on the article
							page in the website directly.
						</small>
					</div>
					<FiX className={styles.closeBtn} onClick={handleClose} />
				</div>
				<div className={`${styles.cardBody} !p-6  overflow-y-auto`}>
					<PhotoGalleryManager />
				</div>
				{/* <div className={styles.footer}>
                <div className={styles.buttonGroup}>
                    <Button
                        variant="secondary"
                        size="sm"
                        rounded="full"
                        customClasses="!px-8"
                        onClick={handleClose}
                    >
                        Cancel
                    </Button>

                    <Button
                        variant="primary"
                        size="sm"
                        rounded="full"
                        customClasses="!px-8"
                        onClick={handleClose}
                    >
                        Save
                    </Button>
                </div>
            </div> */}
			</div>
		</div>
	);
};

export default PhotoGalleryPopup;
