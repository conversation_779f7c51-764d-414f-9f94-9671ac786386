import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { usePostCategoriesMutation } from "../../store/apis/storiesApi";
import StarButton from "./StarButton";
import {
	setErrors,
	setSecondarySubCategories,
	setSubCategory,
	toggleSecondarySubCategory,
} from "../../store/slices/storiesSlice";
import { setVideoSubCategory } from "../../store/slices/videoStorySlice";
import { storyType, storyTypeNumbers } from "../../utils/constants";
import { setWebStoryError, setWebStorySubCategory } from "../../store/slices/webstorySlice";

const SubCategories = ({ type = "stories", isShorts = false }) => {
	const dispatch = useDispatch();
	const [postCategories, { isLoading, isError, error, data }] = usePostCategoriesMutation();

	const {
		storiesState: { secondarySubCategories, subcategory },
		errors,
	} = useSelector((state) => state[storyType[type]]);
	console.log(secondarySubCategories, " seconaqq");

	// fething the data from the api for the categories
	useEffect(() => {
		postCategories({
			categoryType: isShorts ? 2 : storyTypeNumbers[type] ?? null,
		});
	}, [type, postCategories]);

	// setting the subcategory
	const handleSubCategoryClick = (subCategoryId) => {
		if (type === "videoStory") {
			dispatch(setVideoSubCategory(subCategoryId));
		}
		if (type === "stories") {
			dispatch(toggleSecondarySubCategory(subCategoryId));
		}
		if (type === "webStory") {
			dispatch(setWebStorySubCategory(subCategoryId));
			dispatch(setWebStoryError({ ...errors, subcategory: null }));
		}
	};

	return (
		<div className="py-5">
			<h3>Assign story to one or more subcategories for readers to find them.</h3>
			<div className="text-sm flex flex-col gap-y-4 mt-4 ">
				{data
					? data.data.map((category) => {
							return (
								<div key={category._id}>
									<div className="text-[#00000080]">{category.name}</div>
									<div className="pl-2 mt-2 flex flex-col gap-y-2 w-full">
										{category.subcategory.map((subCategory) => {
											return (
												<div
													key={subCategory._id}
													className="flex items-center gap-x-3 group w-full justify-between pr-6"
												>
													<div className="flex items-center gap-x-2">
														<input
															type="checkbox"
															value={subCategory._id}
															name={"secondarySubCategories"}
															disabled={subCategory._id === subcategory}
															id={subCategory._id}
															checked={
																secondarySubCategories.includes(subCategory._id) ||
																subCategory._id === subcategory
															}
															className="w-4 h-4"
															onChange={() => handleSubCategoryClick(subCategory._id)}
														/>{" "}
														<label htmlFor={subCategory._id} className="text-fadeGray">
															{subCategory.name}
														</label>
													</div>
													{/* {showStarButton[type] ? (
														<div
															className={`group-hover:block ${
																highligthedSubCategory === subCategory._id ? "block" : "hidden"
															}`}
														>
															<StarButton
																type={type}
																highligthedSubCategory={highligthedSubCategory}
																isHighlighted={isHighlighted || false}
																subCategoryId={subCategory._id}
															/>
														</div>
													) : null} */}
												</div>
											);
										})}
									</div>
								</div>
							);
					  })
					: null}
			</div>
		</div>
	);
};

export default SubCategories;
