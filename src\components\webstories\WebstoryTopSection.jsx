import React, { useEffect, useState } from "react";
import "react-quill/dist/quill.snow.css";
import { useDispatch, useSelector } from "react-redux";
import { Input } from "../../parts/FormComponents";

import Select from "react-select";
import { useGetAuthorsListQuery } from "../../store/apis/authorsApi";
import { CustomOption } from "../../utils/TransformData";
import { formatDateTime, generateSlugStories } from "../../utils/helperFunctions";
import {
	setCoverImg,
	setCroppedImage,
	setEditCoverImg,
	setErrors,
	setInputData,
	setStatus,
	setWebStoryMetaData,
	setWebStoryOgContent,
	setWebStoryXContent,
} from "../../store/slices/webstorySlice";
import ImageDrop from "../stories/ImageDrop";
import CoverImageCropper from "../stories/CoverImageCropper";
import ScheduleModal from "../stories/ScheduleModal";
import { BiX } from "react-icons/bi";

const WebstoryTopSection = () => {
	const dispatch = useDispatch();
	const [isPublish, setIsPublish] = useState(false);
	// fething the authors
	const { data, isLoading, isError, error, isFetching } = useGetAuthorsListQuery({
		search: "",
		limit: 100,
	});
	const {
		errors,
		status,
		croppedImg,
		storiesState: { title, writer, publishDate },
		coverImg,
	} = useSelector((state) => state.webStory);
	const handleDataChange = ({ value, name }) => {
		dispatch(setInputData({ name, value }));
		dispatch(setErrors({ ...errors, [name]: null }));
	};
	const [authorsState, setAuthorsState] = useState([]);

	// handling the author change here
	const handleAuthorSelectChange = (selected) => {
		const payloadData = selected.map((author) => author.value);
		dispatch(setInputData({ value: payloadData, name: "writer" }));
		dispatch(setErrors({ ...errors, ["writer"]: null }));
	};

	const handleChange = (e) => {
		dispatch(setStatus(e.target.value));
		if (parseInt(e.target.value, 10) === 4) {
			setIsPublish(true);
		}
	};

	useEffect(() => {
		if (data) {
			const transformedData = data.data.map((data) => ({
				value: data._id,
				label: data.name,
			}));

			const initialAuthors = writer.map((author) =>
				transformedData.find((a) => a.value === author)
			);
			setAuthorsState(initialAuthors);
		}
	}, [data, writer]);

	return (
		<>
			{isPublish ? <ScheduleModal setIsPublish={setIsPublish} type={"webStory"} /> : null}
			<CoverImageCropper type={"webStory"} />
			<div className="mt-4 flex gap-x-5 px-2 w-full">
				<div className="border border-[#c1e4fe] rounded-md bg-white w-[60%]">
					<div className="border-b w-full px-5 text-lg font-semibold  py-5">Web Story Info</div>
					<div className="px-5 py-4 flex flex-col gap-y-4">
						<div>
							<Input
								label="Story Title"
								name="title"
								id="title"
								placeholder="Add a story title"
								type="text"
								value={title}
								required={true}
								onDebouncedChange={(value) => {
									handleDataChange({ value, name: "title" });
									dispatch(
										setWebStoryMetaData({
											value: generateSlugStories(value),
											name: "slug",
										})
									);
									dispatch(setWebStoryMetaData({ value, name: "title" }));
									dispatch(setWebStoryOgContent({ value, name: "title" }));
									dispatch(setWebStoryXContent({ value, name: "title" }));
								}}
							/>
							{errors.title && <p className="text-red-500 text-sm">{errors.title}</p>}
						</div>
						<div>
							<div className="text-sm text-fadeGray mb-4">Status</div>
							<select
								value={status}
								onChange={handleChange}
								id="status"
								className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary focus:border-primary focus:outline-none block w-full p-2.5"
							>
								<option selected value={""}>
									Select
								</option>
								<option value={1}>Published</option>
								<option value={0}>Unpublished</option>
								<option value={3}>Draft</option>
								<option value={4}>Scheduled</option>
							</select>

							{status === parseInt(4, 10) && publishDate?.length > 0 ? (
								<p className="mt-1 text-sm text-[#ea7d00]">{formatDateTime(publishDate)}</p>
							) : null}
						</div>
						<div>
							<div className="text-sm text-fadeGray mb-4">Authors</div>
							<Select
								// defaultValue={[colourOptions[2], colourOptions[3]]}
								isMulti
								value={authorsState}
								name="authors"
								options={
									data
										? data.data.map((author) => ({
												value: author._id,
												label: author.name,
												image: author.imgsrc,
										  }))
										: []
								}
								className="basic-multi-select focus:outline-none active:outline-none focus:border-primary focus:!border"
								classNamePrefix="select"
								onChange={handleAuthorSelectChange}
								components={{
									Option: CustomOption,
								}}
							/>
							{errors.writer && <p className="text-red-500 text-sm">{errors.writer}</p>}
						</div>
					</div>
				</div>
				<div className="border border-[#c1e4fe] rounded-md bg-white w-[40%]">
					<div className="border-b w-full px-5 text-lg font-semibold  py-5">
						{croppedImg?.length > 0 ? "Cropped Image" : "Cover Image"}
					</div>
					<div className="xl:px-24 px-10 md:px-14 py-4 imagedrop relative  rounded">
						{croppedImg?.length > 0 ? (
							<div>
								<BiX
									onClick={() => dispatch(setCroppedImage(null))}
									className="absolute right-[5.5rem] top-2 bg-lightBlue rounded-full  text-2xl text-primaryLight hover:text-dangerHover cursor-pointer transition-all duration-500"
								/>
								<img
									src={
										typeof croppedImg === "string" ? croppedImg : URL.createObjectURL(croppedImg[0])
									}
									alt="Cover Image"
									className="h-60 w-60 mx-auto"
								/>
							</div>
						) : (
							<ImageDrop
								selectedFiles={coverImg}
								setSelectedFiles={(value) => {
									dispatch(setCoverImg(value));
								}}
								isEdit={true}
								onEditClick={() => dispatch(setEditCoverImg(true))}
								label={false}
								customHeight="370px"
								customHeight1={"370px"}
								customClasses="relative !h-[370px]"
							/>
						)}
					</div>
					{/* <div className="mt-4">
          <div className="flex flex-col gap-y-4">
            {croppedImg && croppedImg.length > 0 ? (
              <>
                <label
                  htmlFor="cropImg"
                  className="text-sm text-gray-600 flex items-center"
                >
                  Cropped Image Preview
                </label>
                <img
                  src={
                    typeof croppedImg === "string"
                      ? croppedImg
                      : URL.createObjectURL(croppedImg[0])
                  }
                  alt="Cropped Image"
                  className="h-48 w-48 mx-auto"
                />
              </>
            ) : null}
          </div>
        </div> */}
				</div>
			</div>
		</>
	);
};

export default WebstoryTopSection;
