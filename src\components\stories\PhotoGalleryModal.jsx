import React, { useState, useEffect } from "react";
import { RxCross2 } from "react-icons/rx";
import { useDispatch } from "react-redux";
import Button from "../../parts/Button";
import { Input } from "../../parts/FormComponents";
import ImageDrop from "./ImageDrop";
import { updatePhotoGalleryItem } from "../../store/slices/storiesSlice";

const PhotoGalleryModal = ({ isOpen, onClose, imageData, imageIndex }) => {
	const dispatch = useDispatch();
	const [formData, setFormData] = useState({
		imagePath: [],
		title: "",
		caption: "",
		altName: "",
		courtesy: "",
	});
	const [errors, setErrors] = useState({});

	useEffect(() => {
		if (imageData) {
			setFormData({
				imagePath:
					typeof imageData.imagePath === "string" ? [imageData.imagePath] : [imageData.imagePath],
				title: imageData.title || "",
				caption: imageData.caption || "",
				altName: imageData.altName || "",
				courtesy: imageData.courtesy || "",
			});
		}
	}, [imageData]);

	if (!isOpen) return null;

	const handleDataChange = (field, value) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		if (errors[field]) {
			setErrors((prev) => ({ ...prev, [field]: "" }));
		}
	};

	const validateForm = () => {
		const newErrors = {};
		if (!formData.imagePath || (Array.isArray(formData.imagePath) && !formData.imagePath.length)) {
			newErrors.imagePath = "Image is required";
		}
		if (!formData.title.trim()) newErrors.title = "Title is required";
		if (!formData.caption.trim()) newErrors.caption = "Caption is required";
		if (!formData.altName.trim()) newErrors.altName = "Alt name is required";
		return newErrors;
	};

	const handleSave = () => {
		const validationErrors = validateForm();
		if (Object.keys(validationErrors).length > 0) {
			setErrors(validationErrors);
			return;
		}

		const updatedData = {
			...imageData,
			imagePath: formData.imagePath[0] || formData.imagePath,
			title: formData.title,
			caption: formData.caption,
			altName: formData.altName,
			courtesy: formData.courtesy,
		};

		dispatch(updatePhotoGalleryItem({ index: imageIndex, data: updatedData }));
		onClose();
	};

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
			<div className="bg-white rounded-lg w-[70%] max-w-4xl p-6 max-h-[90vh] overflow-y-auto">
				<div className="flex justify-between items-center mb-6">
					<h2 className="text-xl font-semibold">Edit Image</h2>
					<button onClick={onClose} className="text-gray-500 hover:text-gray-700">
						<RxCross2 size={24} />
					</button>
				</div>

				<div className="flex gap-6">
					{/* Left Side - Image */}
					<div className="w-[40%]">
						<div className="w-64 h-96 aspect-auto photoGalleryImageDrop">
							<ImageDrop
								selectedFiles={formData.imagePath}
								setSelectedFiles={(files) => handleDataChange("imagePath", files)}
								label={false}
								customHeight="24rem"
								customHeight1="24rem"
								customClasses="relative !h-[24rem]"
							/>
						</div>

						{errors.imagePath && <p className="text-red-500 text-sm mt-1">{errors.imagePath}</p>}
					</div>

					{/* Right Side - Form */}
					<div className="w-[60%] space-y-4">
						<div>
							<Input
								label="Title"
								value={formData.title}
								onDebouncedChange={(value) => handleDataChange("title", value)}
								placeholder="Enter image title"
								required
							/>
							{errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
						</div>

						<div>
							<Input
								label="Caption"
								value={formData.caption}
								onDebouncedChange={(value) => handleDataChange("caption", value)}
								placeholder="Enter image caption"
								required
							/>
							{errors.caption && <p className="text-red-500 text-sm mt-1">{errors.caption}</p>}
						</div>

						<div>
							<Input
								label="Alt Name"
								value={formData.altName}
								onDebouncedChange={(value) => handleDataChange("altName", value)}
								placeholder="Enter alt text"
								required
							/>
							{errors.altName && <p className="text-red-500 text-sm mt-1">{errors.altName}</p>}
						</div>

						<div>
							<Input
								label="Courtesy"
								value={formData.courtesy}
								onDebouncedChange={(value) => handleDataChange("courtesy", value)}
								placeholder="Enter image courtesy"
							/>
						</div>
					</div>
				</div>

				<div className="flex justify-end mt-6 gap-3">
					<Button
						onClick={onClose}
						variant="secondary"
						size="sm"
						rounded="full"
						customClasses="px-6 py-2"
					>
						Cancel
					</Button>
					<Button
						size="sm"
						onClick={handleSave}
						rounded="full"
						customClasses="bg-primary text-white px-6 py-2"
					>
						Save
					</Button>
				</div>
			</div>
		</div>
	);
};

export default PhotoGalleryModal;
