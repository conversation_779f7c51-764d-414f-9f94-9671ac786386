import React from "react";
import Add from "./Add";
import { useSelector } from "react-redux";
import Categories from "./Categories";
import Flags from "./Flags";
import Tags from "./Tags";
import Settings from "./BasicSettings";
import StorySettings from "./StorySettings";
import Templates from "./Templates";
import SEO from "./SEO";
import Reviews from "./Reviews";
import SubCategories from "./SubCategories";

const RenderTabs = ({ editor }) => {
	const { selectedStoryTab } = useSelector((state) => state.stories);
	const renderTabs = {
		Add: <Add editor={editor} />,
		Categories: <Categories isVideo={false} type="stories" />,
		SubCategories: <SubCategories isVideo={false} type="stories" />,
		Flags: <Flags />,
		Tags: <Tags />,
		Settings: <StorySettings />,
		Templates: <Templates />,
		Reviews: <Reviews />,
		SEO: <SEO />,
		// Flags: <div>Flags</div>,
		// Tags: <div>Tags</div>,
		// Settings: <div>Settings</div>,
	};
	return <div className="px-2 py-3 bg-white scrollbar">{renderTabs[selectedStoryTab]}</div>;
};

export default RenderTabs;
