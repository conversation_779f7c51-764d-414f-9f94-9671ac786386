import React, { useState, useMemo } from "react";
import { useDropzone } from "react-dropzone";
import { MdDeleteOutline } from "react-icons/md";
import { RoundedIconsButton } from "../../parts/Button";
import { GoPlus } from "react-icons/go";
import { LiaExchangeAltSolid } from "react-icons/lia";
import { FiEdit } from "react-icons/fi";

const ImageDrop = ({
  selectedFiles,
  setSelectedFiles,
  label = true,
  customHeight = null,
  customClasses = null,
  customHeight1 = null,
  isEdit = false,
  onSave = () => {},
  onEditClick = () => {},
  customImgClass = null,
}) => {
  const [error, setError] = useState(null);

  const onDrop = (acceptedFiles) => {
    const file = acceptedFiles[0];

    if (file) {
      if (file.size > 700 * 1024) {
        setError("File size must be less than 700KB");
        return;
      }

      setError(null);
      setSelectedFiles([file]);
    }
  };

  const {
    getRootProps,
    getInputProps,
    isFocused,
    isDragAccept,
    isDragReject,
    isDragActive,
    open,
  } = useDropzone({
    accept: { "image/*": [] },
    maxFiles: 1,
    onDrop,
  });

  const replaceFile = () => {
    setSelectedFiles([]); // Reset the selected file or URL

    open();
  };

  const removeFile = () => {
    setSelectedFiles([]); // Remove the selected file or URL
  };

  const baseStyle = {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    padding: "20px",
    height: customHeight ? customHeight : "180px",
    borderWidth: 2,
    borderRadius: 5,
    borderColor: "#3b82f6",
    backgroundColor: "#f4f7ff",
    color: "#bdbdbd",
    outline: "none",
    cursor: "pointer",
    transition: "border .24s ease-in-out",
  };

  const focusedStyle = {
    borderColor: "fc6e00",
  };

  const acceptStyle = {
    borderColor: "#00e676",
  };

  const rejectStyle = {
    borderColor: "#ff1744",
  };

  const style = useMemo(
    () => ({
      ...baseStyle,
      ...(isFocused ? focusedStyle : {}),
      ...(isDragAccept ? acceptStyle : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isFocused, isDragAccept, isDragReject]
  );

  return (
    <>
      {label ? (
        <div className="text-sm text-fadeGray mb-3">Featured Image</div>
      ) : null}
      {selectedFiles && selectedFiles.length > 0 ? (
        <div className="relative flex justify-center group ">
          <div className="hidden group-hover:block transition-all duration-200">
            <div className="inset-0 flex justify-center h-full w-full bg-slate-600 bg-opacity-50 absolute top-0 left-0 z-20">
              <div className="flex items-center gap-x-4 z-30">
                <RoundedIconsButton onClick={replaceFile}>
                  <LiaExchangeAltSolid title="Replace" className="text-xl" />
                </RoundedIconsButton>
                <RoundedIconsButton onClick={removeFile}>
                  <MdDeleteOutline className="text-xl" title="Delete" />
                </RoundedIconsButton>
                {isEdit ? (
                  <RoundedIconsButton onClick={onEditClick}>
                    <FiEdit className="text-lg" />
                  </RoundedIconsButton>
                ) : null}
              </div>
            </div>
          </div>
          <div className={`${customClasses}`}>
            <img
              src={
                typeof selectedFiles === "string"
                  ? selectedFiles // Use URL directly
                  : Array.isArray(selectedFiles) && selectedFiles[0]
                  ? (typeof selectedFiles[0] === "string" 
                     ? selectedFiles[0] 
                     : URL.createObjectURL(selectedFiles[0]))
                  : "" // Fallback for empty or invalid data
              }
              alt="Selected file"
              style={{
                height: customHeight1 ? customHeight1 : "180px", // Default height
                ...((typeof customImgClass === "object" && customImgClass) ||
                  {}), // Merge optional styles
              }}
            />
          </div>
        </div>
      ) : (
        <div {...getRootProps({ style })}>
          <div className="border border-primary border-dashed w-full h-full flex items-center justify-center">
            <input {...getInputProps()} />
            {isDragActive ? (
              <p>Drop the files here ...</p>
            ) : (
              <GoPlus className="text-4xl" />
            )}
          </div>
        </div>
      )}
      {error && <div className="text-sm text-red-400 mt-2">{error}</div>}
    </>
  );
};

export default ImageDrop;
