import React from "react";
import {
	DndContext,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	arrayMove,
	SortableContext,
	sortableKeyboardCoordinates,
	horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import { restrictToHorizontalAxis } from "@dnd-kit/modifiers";
import PhotoGallerySortableItem from "./PhotoGallerySortableItem";

const PhotoGalleryReorder = ({ images, onReorder }) => {
	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		})
	);

	const handleDragEnd = (event) => {
		const { active, over } = event;

		if (active.id !== over.id) {
			const oldIndex = images.findIndex((_, index) => index.toString() === active.id);
			const newIndex = images.findIndex((_, index) => index.toString() === over.id);

			onReorder(arrayMove(images, oldIndex, newIndex));
		}
	};

	return (
		<div>
			<DndContext
				sensors={sensors}
				collisionDetection={closestCenter}
				onDragEnd={handleDragEnd}
				modifiers={[restrictToHorizontalAxis]}
			>
				<SortableContext
					items={images.map((_, index) => index.toString())}
					strategy={horizontalListSortingStrategy}
				>
					<div className="flex gap-2 overflow-x-auto pb-2">
						{images.map((image, index) => (
							<PhotoGallerySortableItem
								key={index}
								id={index.toString()}
								index={index}
								image={image}
							/>
						))}
					</div>
				</SortableContext>
			</DndContext>
		</div>
	);
};

export default PhotoGalleryReorder;
