import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GrDrag } from "react-icons/gr";

const PhotoGallerySortableItem = ({ id, index, image }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex-shrink-0 w-16 bg-white rounded-md border shadow-sm ${
        isDragging ? "z-10 shadow-lg" : ""
      }`}
      {...attributes}
    >
      <div className="p-2 flex flex-col items-center justify-center h-16">
        <span className="text-lg font-bold text-primary mb-2">{index + 1}</span>
        <div {...listeners} className="cursor-grab active:cursor-grabbing">
          <GrDrag size={12} className="text-gray-400" />
        </div>
      </div>
    </div>
  );
};

export default PhotoGallerySortableItem;